\documentclass[conference]{IEEEtran}
\IEEEoverridecommandlockouts
% The preceding line is only needed to identify funding in the first footnote. If that is unneeded, please comment it out.
\usepackage{cite}
\usepackage{amsmath,amssymb,amsfonts}
\usepackage{algorithmic}
\usepackage{graphicx}
\usepackage{textcomp}
\usepackage{xcolor}
\usepackage{booktabs}
\usepackage{multirow}
\usepackage{array}
\def\BibTeX{{\rm B\kern-.05em{\sc i\kern-.025em b}\kern-.08em
    T\kern-.1667em\lower.7ex\hbox{E}\kern-.125emX}}
\begin{document}

\title{A Real-Time Weight Prediction System for Medical Packaging Using Load Cells and Machine Learning\\
{\footnotesize \textnormal{}}
\thanks{Identify applicable funding agency here. If none, delete this.}
}

\author{\IEEEauthorblockN{1\textsuperscript{st} Given Name Surname}
\IEEEauthorblockA{\textit{dept. name of organization (of Aff.)} \\
\textit{name of organization (of Aff.)}\\
City, Country \\
email address or ORCID}
\and
\IEEEauthorblockN{2\textsuperscript{nd} Given Name Surname}
\IEEEauthorblockA{\textit{dept. name of organization (of Aff.)} \\
\textit{name of organization (of Aff.)}\\
City, Country \\
email address or ORCID}
\and
\IEEEauthorblockN{3\textsuperscript{rd} Given Name Surname}
\IEEEauthorblockA{\textit{dept. name of organization (of Aff.)} \\
\textit{name of organization (of Aff.)}\\
City, Country \\
email address or ORCID}
}

\maketitle

\begin{abstract}
This paper presents a novel real-time weight prediction system designed specifically for medical packaging applications, leveraging advanced load cell technology, sophisticated digital signal processing, and ensemble machine learning models. The system's primary objective is to ensure precise weight measurement in dynamic environments, mitigating the impact of various noise sources and environmental factors. Data acquisition is facilitated by high-resolution Analog-to-Digital Converters (ADCs), followed by a multi-stage digital signal filtering pipeline inspired by techniques used in Electrocardiogram (ECG) anomaly detection to enhance signal clarity and interpretability. The filtered data then undergoes comprehensive feature extraction, yielding both time and frequency domain characteristics. For weight prediction, an ensemble learning approach is employed, utilising Decision Trees (DT), Random Forest (RF), Gradient Boosting (GB), and XGBoost (XGB) as base learners. These diverse models are then combined through a stacking meta-ensemble technique, with Linear Regression (LR) serving as the meta-learner, optimizing overall predictive performance. Hyperparameter tuning is performed using GridSearchCV to ensure optimal model configurations, enhancing the system's robustness and accuracy. Experimental results demonstrate the system's superior performance in terms of R², Mean Absolute Error (MAE), and Root Mean Squared Error (RMSE), validating its potential for reliable and efficient real-time weight monitoring in critical medical packaging lines. This system offers significant advancements in quality control and operational efficiency for high-stakes industrial processes.
\end{abstract}

\begin{IEEEkeywords}
load cells, machine learning, ensemble learning, signal processing, medical packaging, weight prediction, real-time systems
\end{IEEEkeywords}

\section{Introduction}

The demand for accurate and reliable weight measurement systems is paramount in critical industrial sectors, particularly in medical packaging, where precision directly impacts product quality, regulatory compliance, and patient safety. Traditional weighing systems, often reliant on mechanical or analogue components, have seen significant advancements with the advent of digital technologies, enhancing their accuracy, reliability, and scalability. However, the dynamic nature of modern production lines, such as conveyor belts or automated packaging systems, introduces inherent challenges that can compromise measurement integrity. These challenges primarily stem from environmental noise, including machine vibrations, temperature fluctuations, and electromagnetic interference, which can significantly corrupt the load cell's signal and lead to inaccuracies. Furthermore, calibration drift over time necessitates frequent recalibration, adding to operational costs and potential downtime. The imperative for real-time data processing, capable of providing immediate feedback or initiating actions based on precise weight measurements, further complicates system design.

To address these multifaceted challenges, this research proposes and evaluates a novel real-time weight prediction system for medical packaging. Our motivation stems from the critical need for uncompromised accuracy and efficiency in dynamic industrial environments, where even minor discrepancies can have significant repercussions. This system integrates state-of-the-art load cell technology with an innovative signal processing pipeline inspired by robust methodologies from ECG anomaly detection, and an ensemble machine learning framework for enhanced predictive capabilities. The primary contributions of this paper include:
\begin{itemize}
    \item The design and implementation of a robust data acquisition and digital filtering pipeline tailored for dynamic weighing environments.
    \item The adaptation of noise cancellation and signal enhancement techniques from ECG signal analysis to improve the quality of load cell data.
    \item The development of an ensemble learning model employing diverse base learners (DT, RF, GB, XGB) combined via stacking with Linear Regression for superior weight prediction accuracy and generalization.
    \item A comprehensive evaluation demonstrating the system's real-time capabilities and its effectiveness in mitigating environmental noise and calibration drift through intelligent algorithms.
\end{itemize}

\section{Related Work}

The integration of advanced sensing technologies with machine learning (ML) has garnered significant attention across various domains, revolutionising data analysis and predictive capabilities. In sensor-based applications, ML has proven instrumental in enhancing performance and interpretability. For instance, in sitting posture monitoring systems (SPMSs), load cells combined with ML algorithms, such as Support Vector Machines (SVMs) and Random Forests, have demonstrated high classification rates for various sitting postures, even with a reduced number of sensors. Similarly, in critical infrastructure, hybrid ensemble learning models have been proposed for anomaly detection in industrial sensor networks and Supervisory Control and Data Acquisition (SCADA) systems, utilizing diverse datasets and demonstrating high accuracy in identifying cyberattacks. The motivation of the paper also mentions using the system to detect intrusions that have bypassed conventional IDS.

The robustness of signal acquisition and processing is a foundational element for accurate sensor-based systems. Electrocardiogram (ECG) signals, for instance, are notoriously susceptible to various noise sources, including baseline wander, powerline interference, muscle noise, and motion artefacts, which can severely obscure critical waveform features essential for accurate diagnosis. Consequently, sophisticated noise cancellation filters are crucial for enhancing ECG signal quality. Low-pass filters are commonly employed to eliminate high-frequency noise while preserving vital lower-frequency components. Kalman filters offer an adaptive approach, predicting and correcting signal variations, proving effective against time-varying noise and baseline wander. Moving average filters provide a simpler method for smoothing signals by averaging neighbouring data points, reducing high-frequency artefacts, and being efficient for real-time processing. Band-stop filters target specific narrow-band interference, such as powerline noise, without significantly impacting the overall signal integrity. The combination of these filtering techniques significantly improves signal clarity, which is indispensable for subsequent feature extraction and analysis.

In the realm of machine learning, ensemble learning has emerged as a powerful paradigm for improving predictive performance. Instead of relying on a single model, ensemble methods combine the predictions from two or more base models, typically through voting or averaging, to achieve superior accuracy and better generalisation capabilities. A key insight underpinning their effectiveness is the principle of accuracy and diversity: an ensemble performs better than its individual members if the classifiers are accurate and make different errors on new data points. Common ensemble methods include Bagging, Boosting, and Stacking. Bagging (Bootstrap Aggregating), exemplified by Random Forest, builds multiple base learners from randomly generated training subsets and combines their outputs, typically via majority voting, to reduce variance and prevent overfitting. Boosting, such as AdaBoost and Gradient Boosting, sequentially trains base learners, with each new learner focusing on improving predictions for instances misclassified by previous models, effectively reducing bias. Stacking (Stacked Generalisation), a type of heterogeneous ensemble, utilizes a meta-level model to combine predictions from diverse base-level classifiers, learning the optimal way to blend their outputs. This approach has been widely applied in clinical data analysis to predict outcomes like mortality and severe cardiac events, demonstrating its potential for robust predictive performance. The present study builds upon these foundational principles by applying and integrating these advanced techniques to the challenging domain of real-time weight prediction for medical packaging.

\section{Load Cell \& Bending Beam Sensor}

Load cells are fundamental transducers in modern weighing systems, serving as the primary mechanism for converting physical force or weight into a measurable electrical signal. This conversion is typically achieved through the deformation of a strain-sensitive element within the load cell. Among various types, strain gauge load cells are the most prevalent in industrial weighing applications due to their high accuracy, sensitivity, and cost-effectiveness. These devices incorporate metal strain gauges (e.g., with a nominal resistance of 350 Ω), which are precisely bonded to a carefully designed mechanical structure, such as a bending beam. When a force is applied, the structure deforms, causing a corresponding change in the electrical resistance of the strain gauges.

In a common configuration, these strain gauges are wired into a Wheatstone bridge circuit, which translates minute resistance changes into a voltage output. For instance, a four-arm bridge configuration amplifies these small changes into a measurable off-null voltage that is directly proportional to the applied load. Load cells come in various designs adapted to specific applications, including bending beam, shear beam, s-beam (or 'z' beam), canister, and platform types. For applications in medical packaging, bending beam or platform load cells are commonly employed, mounted beneath a weighing platform or conveyor system to measure the weight of products as they pass.

The physical characteristics of the load cell's material, such as 2024-T4 aluminium, and its components, including the resistive metal foil (e.g., Cu-Ni or Ni-Cr) and the polyimide backing with epoxy adhesive, are crucial for its performance. These components exhibit time-dependent relaxation (creep), which can limit accuracy over time, particularly in weighing applications. Manufacturers employ techniques like adjusting the length of the strain gauge end loops to minimise creep. Furthermore, load cells are particularly susceptible to environmental factors, such as machine vibration inherent in harvesting equipment or packaging lines. This harmonic motion can induce variations in the sensor output, directly impacting measurement precision. Therefore, the inherent design and material properties, alongside external environmental influences, define the operational characteristics and potential limitations of load cells in real-time weight prediction systems.

\section{Application \& Accuracy}

The proposed real-time weight prediction system offers substantial benefits for medical packaging applications, primarily by enhancing quality control and operational efficiency. In automated packaging lines, such a system can provide immediate and precise weight measurements for each package, enabling instant identification of under- or over-filled items. This capability is critical for maintaining product consistency in pharmaceutical manufacturing, where precision dosing and formulation are non-negotiable. Beyond manufacturing, accurate real-time weighing assists in logistics and inventory management, ensuring correct package weights for shipping and tracking purposes. The system's ability to operate in real-time on dynamic conveyor belts or moving platforms is a key advantage, providing immediate feedback or triggering actions based on continuous weight data.

A significant aspect of ensuring the system's long-term reliability and precision is its approach to calibration. Traditional load cells are prone to calibration drift caused by mechanical wear, temperature variations, or electrical noise, necessitating frequent manual recalibration. Our proposed system addresses this by leveraging Artificial Intelligence (AI) models that can dynamically adjust calibration in real-time. These AI models learn from historical data and detect patterns in environmental changes, enabling automatic recalibration based on factors like temperature variations or detected weight drifts. This capability significantly reduces the need for manual intervention, thereby improving system efficiency and extending the operational lifespan of the equipment by enabling predictive maintenance.

The precision of measurements is paramount in medical packaging. The system aims to achieve high accuracy even in the presence of challenging environmental noise. By integrating intelligent controllers powered by AI/ML algorithms, the system can continuously monitor its output and make real-time adjustments to maintain accuracy and reliability across various operational conditions. This adaptive nature means the system is not only accurate at the point of measurement but also maintains its high performance over time and under varying industrial conditions, making it a robust solution for the demanding requirements of medical packaging.

\section{Analog to Digital Conversion}

The crucial interface between the analogue electrical signal generated by the load cells and the digital processing capabilities of the system is the Analog-to-Digital Converter (ADC). Load cells produce a very small analogue voltage signal in response to applied force. This signal is first amplified and conditioned by an operational or instrumentation amplifier before being fed into the ADC. The ADC's role is to convert this amplified analogue signal into a digital format that can be processed by a microcontroller. High-precision ADCs, such as the ADS1256, are preferred for weighing applications to ensure that the minute changes in the load cell's output are captured with sufficient resolution, directly impacting the accuracy of the weight measurement.

Sampling rate is a critical parameter in the ADC process. A high sampling rate is essential to capture the dynamic behaviour of the load cell in a real-time environment and to prevent aliasing, where high-frequency components in the analogue signal are misrepresented as lower frequencies in the digital signal. In a dynamic weighing system, such as one for medical packaging on a conveyor belt, signals can change rapidly, necessitating sampling rates sufficiently higher than the highest frequency component of interest or potential noise. For instance, previous work in yield monitoring for combines noted significant harmonic components in machine vibration up to 30 Hz, leading to a sampling rate of 250 Hz to prevent aliasing. This provides a precedent for selecting appropriate sampling rates in our system to capture all relevant information and noise profiles.

The ECG-inspired data pipeline emphasizes the necessity of robust preprocessing, directly influencing the choice and configuration of the ADC. In ECG analysis, signals are frequently corrupted by noise during acquisition, demanding sophisticated noise cancellation techniques to enhance interpretability. Similarly, load cell signals in industrial settings are prone to various forms of interference, making their raw output inherently noisy. By adopting a pipeline philosophy similar to that used for ECG signals, which prioritizes cleaning raw data to mitigate the effects of noise that can obscure critical signal characteristics, we ensure that the digital data fed into subsequent filtering and machine learning stages is of the highest possible quality. This involves careful consideration of the ADC's resolution and speed to facilitate the effective application of downstream digital signal processing techniques.

\section{Signal Filtering}

Signal filtering is a crucial step in enhancing the reliability and accuracy of data acquired from load cells in noisy industrial environments. Raw load cell signals are inherently susceptible to various forms of interference, including machine vibration, electromagnetic pick-up, thermal instability, and conversion noise. Effective filtering is required to provide both a stable weight reading (large time constant) and the ability to rapidly track changes in weight (short time constant). This study implements several digital filtering techniques, drawing inspiration from their successful application in Electrocardiogram (ECG) signal processing for noise cancellation.

Low-Pass Filters (LPF) are fundamental in this process, designed to remove high-frequency noise components while preserving the lower-frequency information essential for weight analysis. The selection of an appropriate cutoff frequency for an LPF is critical; for instance, a 50 Hz cutoff is typical to filter out high-frequency noise. For smoothing out random fluctuations, Moving Average (MA) filters are employed. These filters operate by averaging successive data points, effectively reducing high-frequency artifacts and smoothing the signal. While simpler, MA filters can introduce signal distortion in sharp transitions if not carefully applied.

For more sophisticated noise reduction, Infinite Impulse Response (IIR) filters, such as Butterworth and Elliptic filters, are considered. Butterworth filters are known for their maximally flat frequency response in the passband, making them ideal for signal smoothing without introducing ripples. In contrast to purely analogue solutions which can be complex and may not address conversion noise, digital filters are ideal for treating sampled data and can be implemented in real-time or as post-processed applications.

To effectively design and apply these filters, spectral analysis, often utilizing the Fast Fourier Transform (FFT), is indispensable. FFT allows for the identification of significant frequency components of the noise, such as machine vibrations. For example, in previous studies on yield monitors, spectral analysis revealed dominant harmonic components from machine mechanisms, guiding the selection of sampling rates and filter characteristics. Adaptive filters, like the Self-Adaptive Pseudo-Moving Average Filter (SAPMAF) or Recursive Least-Squares (RLS) lattice adaptive filters, are particularly valuable when noise characteristics are unknown, time-varying, or non-stationary, as is often the case in dynamic industrial environments. These filters can automatically adjust their parameters to continuously improve performance by adapting to the changing noise environment. The filtering stage is paramount in transforming raw, noisy load cell data into a clean, reliable signal suitable for precise weight prediction.

\section{Feature Extraction}

Following the critical signal filtering stage, feature extraction transforms the processed raw data from the load cells into meaningful quantitative features that can be effectively utilized by machine learning models for weight prediction. This process is vital for capturing the underlying patterns and characteristics within the clean signal, enabling the models to learn and generalize accurately. The features can broadly be categorized into time-domain and frequency-domain features.

Time-domain features capture characteristics directly from the signal's amplitude over time. Drawing inspiration from ECG signal analysis, where features like R-R intervals and QRS duration are crucial for identifying cardiac conditions, we adapt this concept to analyse the dynamic load cell signal. For weight prediction, this may involve identifying patterns related to load application and removal over specific time windows. Beyond these specific, application-dependent features, basic statistical measures are extracted to characterize the overall behaviour and variability of the signal. These include:
\begin{itemize}
    \item \textbf{Mean:} Represents the average weight detected over a given time window.
    \item \textbf{Variance/Standard Deviation:} Quantifies the spread or variability of the weight measurements, indicative of signal stability or fluctuations.
    \item \textbf{Skewness:} Measures the asymmetry of the weight distribution, indicating the presence of outliers or biases.
    \item \textbf{Kurtosis:} Describes the "tailedness" of the weight distribution, providing insights into the presence of extreme values.
\end{itemize}

Frequency-domain features reflect the spectral content of the load cell signal, which can be particularly helpful in identifying residual noise patterns or unique characteristics of the load application process. These features are typically derived from the Fourier Transform of the signal, revealing dominant frequencies associated with machine vibrations or other periodic influences that might still be present after initial filtering.

Finally, normalization is an essential preprocessing step for the extracted features. Normalization scales all attribute values to a common range, typically between 0 and 1 using the Min-Max approach. This technique ensures that features with larger numerical ranges do not disproportionately influence the machine learning algorithms, leading to more stable and efficient model training. The application of Principal Component Analysis (PCA) can further be utilized for feature extraction and dimensionality reduction, replacing original attributes with new, more relevant ones while maintaining data integrity. By meticulously extracting and normalizing these comprehensive features, the system is equipped with robust inputs for the subsequent machine learning prediction stage.

\section{Machine Learning Methods}

The core of the real-time weight prediction system relies on an advanced machine learning framework, primarily utilizing ensemble learning techniques due to their proven ability to significantly enhance predictive performance and robustness over single models.

\subsection{Ensemble Learning Overview}
Ensemble learning algorithms combine predictions from multiple base models to obtain improved overall predictive performance and better generalization ability. The effectiveness of an ensemble hinges on two critical conditions: the individual classifiers must be accurate (error rate better than random guessing) and diverse (making different errors on new data points). By combining diverse, accurate models, ensembles can reduce overall bias and variance, leading to more robust and reliable predictions, often outperforming any single classifier.

\subsection{Decision Trees (DT)}
Decision Trees (DT) are fundamental machine learning algorithms that recursively partition data based on feature values to make predictions. They form a tree-like structure where each internal node represents a test on a feature, each branch represents an outcome of the test, and each leaf node represents the final prediction. While interpretable, single Decision Trees can be prone to overfitting, making them effective as base learners within ensemble methods.

\subsection{Random Forest (RF)}
Random Forest (RF) is a powerful and widely-used ensemble learning method that builds upon the concept of Bagging (Bootstrap Aggregating). It constructs multiple decision trees during training, each from a bootstrapped sample of the training data. For a prediction, it combines the outputs of these individual trees, typically through majority voting for classification or averaging for regression tasks. RF is highly effective for large, high-dimensional datasets and is much less prone to overfitting than single decision trees, making it a robust choice for noisy sensor data.

\subsection{Gradient Boosting (GB)}
Gradient Boosting (GB) is a powerful boosting technique that builds an additive approximation of a target function by iteratively adding decision trees. Each new tree is trained to correct the errors (residuals) made by the ensemble of previously trained trees. The algorithm minimises a specified loss function by greedily adding a new tree that best reduces the residuals. This sequential training process allows GB to progressively refine predictions, leading to highly accurate models.

\subsection{XGBoost (XGB)}
XGBoost (XGB) is an optimized and scalable implementation of gradient boosting that has dominated many machine learning competitions. It introduces several advancements over conventional GB, notably the inclusion of a regularization term in its loss function to prevent overfitting and improve generalization. XGBoost also incorporates techniques like parallel tree boosting and cache-aware computing, making it highly efficient for large datasets. Its robustness and high performance make it an excellent choice for complex prediction tasks.

\subsection{Linear Regression (LR) as Stacking Meta-Learner}
Stacking (Stacked Generalisation) is a heterogeneous ensemble technique that combines the predictions of multiple diverse base learners (level-0 models) using a separate meta-learning algorithm (level-1 model). The base learners are trained on the original dataset, and their predictions on out-of-sample data, along with the actual labels, form a new dataset used to train the meta-learner. For this system, Linear Regression (LR) is chosen as the meta-learner. LR is a simple yet effective model that learns a linear relationship between the base learners' predictions and the true output, serving to optimally combine their strengths. The output of the stacked ensemble is the prediction made by the meta-learner, based on the outputs of the base models.

\subsection{GridSearchCV for Tuning}
To ensure the optimal performance of the machine learning models, particularly the base learners and the meta-learner, hyperparameter tuning is essential. While the sources frequently discuss the importance of optimising model parameters to enhance accuracy and efficiency, the specific technique of GridSearchCV (a common external concept in ML for exhaustive hyperparameter search) is not directly detailed within the provided materials. However, its purpose aligns with the objective of improving model performance through systematic parameter selection, as demonstrated by the use of Grey Wolf Optimizer (GWO) for optimizing ensemble learning model parameters in one of the sources. GridSearchCV systematically works through multiple combinations of parameter tunes, cross-validating each combination to determine the best-performing model [Not in sources explicitly]. This comprehensive approach ensures that the selected models are configured for maximum predictive accuracy and robustness, critical for a real-time system.

\end{document}
